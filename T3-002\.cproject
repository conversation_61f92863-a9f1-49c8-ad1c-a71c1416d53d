<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
    <storageModule moduleId="org.eclipse.cdt.core.settings">
        <cconfiguration id="com.ti.ccstudio.buildDefinitions.TMS470.Debug.**********">
            <storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.ti.ccstudio.buildDefinitions.TMS470.Debug.**********" moduleId="org.eclipse.cdt.core.settings" name="Debug">
                <externalSettings/>
                <extensions>
                    <extension id="org.eclipse.cdt.core.GmakeErrorParser" point="com.ti.ccs.project.ErrorParser"/>
                    <extension id="org.eclipse.cdt.core.GASErrorParser" point="com.ti.ccs.project.ErrorParser"/>
                    <extension id="com.ti.ccs.errorparser.SysConfigErrorParser" point="com.ti.ccs.project.ErrorParser"/>
                    <extension id="org.eclipse.cdt.core.GCCErrorParser" point="com.ti.ccs.project.ErrorParser"/>
                    <extension id="com.ti.ccs.errorparser.CompilerErrorParser_TI" point="com.ti.ccs.project.ErrorParser"/>
                </extensions>
            </storageModule>
            <storageModule moduleId="cdtBuildSystem" version="4.0.0">
                <configuration artifactExtension="out" artifactName="${ProjName}" buildProperties="" cleanCommand="${CG_CLEAN_CMD}" description="" id="com.ti.ccstudio.buildDefinitions.TMS470.Debug.**********" name="Debug" parent="com.ti.ccstudio.buildDefinitions.TMS470.Debug">
                    <folderInfo id="com.ti.ccstudio.buildDefinitions.TMS470.Debug.**********." name="/" resourcePath="">
                        <toolChain id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.DebugToolchain.550134409" name="TI Build Tools" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.DebugToolchain" targetTool="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.linkerDebug.1336043258">
                            <option id="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS.587670663" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS" valueType="stringList">
                                <listOptionValue value="DEVICE_CONFIGURATION_ID=Cortex M.MSPM0G3507"/>
                                <listOptionValue value="DEVICE_CORE_ID="/>
                                <listOptionValue value="DEVICE_ENDIANNESS=little"/>
                                <listOptionValue value="OUTPUT_FORMAT=ELF"/>
                                <listOptionValue value="CCS_MBS_VERSION=51.0.0"/>
                                <listOptionValue value="RUNTIME_SUPPORT_LIBRARY="/>
                                <listOptionValue value="OUTPUT_TYPE=executable"/>
                                <listOptionValue value="PRODUCTS=MSPM0-SDK:2.4.0.06;sysconfig:1.21.1;"/>
                                <listOptionValue value="PRODUCT_MACRO_IMPORTS={&quot;MSPM0-SDK&quot;:[&quot;${COM_TI_MSPM0_SDK_INCLUDE_PATH}&quot;,&quot;${COM_TI_MSPM0_SDK_LIBRARY_PATH}&quot;,&quot;${COM_TI_MSPM0_SDK_LIBRARIES}&quot;,&quot;${COM_TI_MSPM0_SDK_SYMBOLS}&quot;,&quot;${COM_TI_MSPM0_SDK_SYSCONFIG_MANIFEST}&quot;],&quot;sysconfig&quot;:[&quot;${SYSCONFIG_TOOL_INCLUDE_PATH}&quot;,&quot;${SYSCONFIG_TOOL_LIBRARY_PATH}&quot;,&quot;${SYSCONFIG_TOOL_LIBRARIES}&quot;,&quot;${SYSCONFIG_TOOL_SYMBOLS}&quot;,&quot;${SYSCONFIG_TOOL_SYSCONFIG_MANIFEST}&quot;]}"/>
                            </option>
                            <option id="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION.465217782" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION" value="TICLANG_4.0.0.LTS" valueType="string"/>
                            <targetPlatform id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.targetPlatformDebug.810689375" name="Platform" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.targetPlatformDebug"/>
                            <builder buildPath="${BuildDirectory}" id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.builderDebug.1567012889" keepEnvironmentInBuildfile="false" name="GNU Make" parallelBuildOn="true" parallelizationNumber="optimal" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.builderDebug"/>
                            <tool id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.compilerDebug.1132205317" name="Arm Compiler" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.compilerDebug">
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.GENERATE_DWARF_DEBUG.611825728" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.GENERATE_DWARF_DEBUG" value="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.GENERATE_DWARF_DEBUG.GDWARF_3" valueType="enumerated"/>
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.ENDIAN_NESS__BIG_LITTLE.604829163" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.ENDIAN_NESS__BIG_LITTLE" value="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.ENDIAN_NESS__BIG_LITTLE.MLITTLE_ENDIAN" valueType="enumerated"/>
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.INCLUDE_PATH.745557190" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.INCLUDE_PATH" valueType="includePath">
                                    <listOptionValue value="${COM_TI_MSPM0_SDK_INCLUDE_PATH}"/>
                                    <listOptionValue value="${SYSCONFIG_TOOL_INCLUDE_PATH}"/>
                                    <listOptionValue value="${PROJECT_ROOT}"/>
                                    <listOptionValue value="${PROJECT_ROOT}/${ConfigName}"/>
                                    <listOptionValue value="${COM_TI_MSPM0_SDK_INSTALL_DIR}/source/third_party/CMSIS/Core/Include"/>
                                    <listOptionValue value="${COM_TI_MSPM0_SDK_INSTALL_DIR}/source"/>
                                    <listOptionValue value="${PROJECT_ROOT}/BSP/Inc"/>
                                    <listOptionValue value="${PROJECT_ROOT}/APP/Inc"/>
                                    <listOptionValue value="${PROJECT_ROOT}/DMP"/>
                                </option>
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.DEFINE.510875151" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.DEFINE" valueType="definedSymbols">
                                    <listOptionValue value="${COM_TI_MSPM0_SDK_SYMBOLS}"/>
                                    <listOptionValue value="${SYSCONFIG_TOOL_SYMBOLS}"/>
                                    <listOptionValue value="MPU6050"/>
                                    <listOptionValue value="MOTION_DRIVER_TARGET_MSPM0"/>
                                </option>
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.OPT_LEVEL.708731952" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.OPT_LEVEL" value="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.OPT_LEVEL.0" valueType="enumerated"/>
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.CMD_FILE.240016818" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.CMD_FILE" valueType="stringList">
                                    <listOptionValue value="device.opt"/>
                                </option>
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.MCPU.6681058" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.MCPU" value="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.MCPU.cortex-m0plus" valueType="enumerated"/>
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.MARCH.1948766294" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.MARCH" value="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.MARCH.thumbv6m" valueType="enumerated"/>
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.MFLOAT_ABI.57864813" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.MFLOAT_ABI" value="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.MFLOAT_ABI.soft" valueType="enumerated"/>
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.SELECT_PROCESSOR_MODE__ARM_THUMB.145221815" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.SELECT_PROCESSOR_MODE__ARM_THUMB" value="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.SELECT_PROCESSOR_MODE__ARM_THUMB.MTHUMB" valueType="enumerated"/>
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.STD_C.1134966226" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.STD_C" value="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.STD_C._none" valueType="enumerated"/>
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.W_LOWERCASE.1505504909" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.W_LOWERCASE" value="true" valueType="boolean"/>
                            </tool>
                            <tool id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.linkerDebug.1336043258" name="Arm Linker" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.linkerDebug">
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.linkerID.OUTPUT_FILE.2066955462" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.linkerID.OUTPUT_FILE" value="${ProjName}.out" valueType="string"/>
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.linkerID.MAP_FILE.498442807" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.linkerID.MAP_FILE" value="${ProjName}.map" valueType="string"/>
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.linkerID.XML_LINK_INFO.2099482573" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.linkerID.XML_LINK_INFO" value="${ProjName}_linkInfo.xml" valueType="string"/>
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.linkerID.DISPLAY_ERROR_NUMBER.1686515307" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.linkerID.DISPLAY_ERROR_NUMBER" value="true" valueType="boolean"/>
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.linkerID.DIAG_WRAP.716253193" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.linkerID.DIAG_WRAP" value="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.linkerID.DIAG_WRAP.off" valueType="enumerated"/>
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.linkerID.REREAD_LIBS.1431794738" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.linkerID.REREAD_LIBS" value="false" valueType="boolean"/>
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.linkerID.SEARCH_PATH.1376151031" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.linkerID.SEARCH_PATH" valueType="libPaths">
                                    <listOptionValue value="${COM_TI_MSPM0_SDK_LIBRARY_PATH}"/>
                                    <listOptionValue value="${SYSCONFIG_TOOL_LIBRARY_PATH}"/>
                                    <listOptionValue value="${COM_TI_MSPM0_SDK_INSTALL_DIR}/source"/>
                                    <listOptionValue value="${PROJECT_ROOT}"/>
                                    <listOptionValue value="${PROJECT_BUILD_DIR}/syscfg"/>
                                    <listOptionValue value="${CG_TOOL_ROOT}/lib"/>
                                </option>
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.linkerID.LIBRARY.916826832" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.linkerID.LIBRARY" valueType="libs">
                                    <listOptionValue value="${COM_TI_MSPM0_SDK_LIBRARIES}"/>
                                    <listOptionValue value="${SYSCONFIG_TOOL_LIBRARIES}"/>
                                    <listOptionValue value="device.cmd.genlibs"/>
                                    <listOptionValue value="libc.a"/>
                                </option>
                            </tool>
                            <tool id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.hex.1929203450" name="Arm Hex Utility" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.hex"/>
                            <tool id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.objcopy.1335563000" name="Arm Objcopy Utility" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.objcopy"/>
                            <tool id="com.ti.ccstudio.buildDefinitions.sysConfig.1093365584" name="SysConfig" superClass="com.ti.ccstudio.buildDefinitions.sysConfig">
                                <option id="com.ti.ccstudio.buildDefinitions.sysConfig.PRODUCTS.1576288837" superClass="com.ti.ccstudio.buildDefinitions.sysConfig.PRODUCTS" valueType="stringList">
                                    <listOptionValue value="${COM_TI_MSPM0_SDK_SYSCONFIG_MANIFEST}"/>
                                    <listOptionValue value="${SYSCONFIG_TOOL_SYSCONFIG_MANIFEST}"/>
                                </option>
                                <option id="com.ti.ccstudio.buildDefinitions.sysConfig.OUTPUT_DIR__MANUAL.899821222" superClass="com.ti.ccstudio.buildDefinitions.sysConfig.OUTPUT_DIR__MANUAL" value="." valueType="string"/>
                                <option id="com.ti.ccstudio.buildDefinitions.sysConfig.DIRECTORY_MODE.1395824773" superClass="com.ti.ccstudio.buildDefinitions.sysConfig.DIRECTORY_MODE" value="com.ti.ccstudio.buildDefinitions.sysConfig.DIRECTORY_MODE.manual" valueType="enumerated"/>
                            </tool>
                        </toolChain>
                    </folderInfo>
                    <folderInfo id="com.ti.ccstudio.buildDefinitions.TMS470.Debug.**********.APP" name="APP" resourcePath="APP">
                        <toolChain id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.DebugToolchain.570548027" name="TI Build Tools" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.DebugToolchain" unusedChildren="">
                            <option id="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS.587670663.1807591264" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS.587670663"/>
                            <option id="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION.465217782.1029292363" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION.465217782"/>
                            <targetPlatform id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.targetPlatformDebug" name="Platform" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.targetPlatformDebug"/>
                            <tool id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.compilerDebug.760858900" name="Arm Compiler" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.compilerDebug.1132205317">
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.INCLUDE_PATH.317651716" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.INCLUDE_PATH" valueType="includePath">
                                    <listOptionValue value="${COM_TI_MSPM0_SDK_INCLUDE_PATH}"/>
                                    <listOptionValue value="${SYSCONFIG_TOOL_INCLUDE_PATH}"/>
                                    <listOptionValue value="${PROJECT_ROOT}"/>
                                    <listOptionValue value="${PROJECT_ROOT}/APP/Inc"/>
                                    <listOptionValue value="${PROJECT_ROOT}/BSP/Inc"/>
                                    <listOptionValue value="${PROJECT_ROOT}/${ConfigName}"/>
                                    <listOptionValue value="${COM_TI_MSPM0_SDK_INSTALL_DIR}/source/third_party/CMSIS/Core/Include"/>
                                    <listOptionValue value="${COM_TI_MSPM0_SDK_INSTALL_DIR}/source"/>
                                    <listOptionValue value="${PROJECT_ROOT}/DMP/Inc"/>
                                </option>
                            </tool>
                            <tool id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.linkerDebug.1720018986" name="Arm Linker" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.linkerDebug.1336043258"/>
                            <tool id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.hex.2126042550" name="Arm Hex Utility" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.hex.1929203450"/>
                            <tool id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.objcopy.1865183149" name="Arm Objcopy Utility" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.objcopy.1335563000"/>
                            <tool id="com.ti.ccstudio.buildDefinitions.sysConfig.810312277" name="SysConfig" superClass="com.ti.ccstudio.buildDefinitions.sysConfig.1093365584"/>
                        </toolChain>
                    </folderInfo>
                    <folderInfo id="com.ti.ccstudio.buildDefinitions.TMS470.Debug.**********.targetConfigs" name="targetConfigs" resourcePath="targetConfigs">
                        <toolChain id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.DebugToolchain.1903540419" name="TI Build Tools" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.DebugToolchain" unusedChildren="">
                            <option id="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS.587670663.1847932392" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS.587670663"/>
                            <option id="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION.465217782.144510866" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION.465217782"/>
                            <targetPlatform id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.targetPlatformDebug" name="Platform" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.targetPlatformDebug"/>
                            <tool id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.compilerDebug.588235156" name="Arm Compiler" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.compilerDebug.1132205317">
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.INCLUDE_PATH.2099943041" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.INCLUDE_PATH" valueType="includePath">
                                    <listOptionValue value="${COM_TI_MSPM0_SDK_INCLUDE_PATH}"/>
                                    <listOptionValue value="${SYSCONFIG_TOOL_INCLUDE_PATH}"/>
                                    <listOptionValue value="${PROJECT_ROOT}"/>
                                    <listOptionValue value="${PROJECT_ROOT}/${ConfigName}"/>
                                    <listOptionValue value="${COM_TI_MSPM0_SDK_INSTALL_DIR}/source/third_party/CMSIS/Core/Include"/>
                                    <listOptionValue value="${COM_TI_MSPM0_SDK_INSTALL_DIR}/source"/>
                                    <listOptionValue value="${PROJECT_ROOT}BSP/Inc"/>
                                    <listOptionValue value="${PROJECT_ROOT}APP/Inc"/>
                                </option>
                            </tool>
                            <tool id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.linkerDebug.1074935387" name="Arm Linker" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.linkerDebug.1336043258"/>
                            <tool id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.hex.542484458" name="Arm Hex Utility" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.hex.1929203450"/>
                            <tool id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.objcopy.1806704997" name="Arm Objcopy Utility" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.objcopy.1335563000"/>
                            <tool id="com.ti.ccstudio.buildDefinitions.sysConfig.399300485" name="SysConfig" superClass="com.ti.ccstudio.buildDefinitions.sysConfig.1093365584"/>
                        </toolChain>
                    </folderInfo>
                    <folderInfo id="com.ti.ccstudio.buildDefinitions.TMS470.Debug.**********.DMP" name="DMP" resourcePath="DMP">
                        <toolChain id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.DebugToolchain.1512060870" name="TI Build Tools" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.DebugToolchain" unusedChildren="">
                            <option id="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS.587670663.1878051672" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS.587670663"/>
                            <option id="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION.465217782.722942240" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION.465217782"/>
                            <targetPlatform id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.targetPlatformDebug" name="Platform" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.targetPlatformDebug"/>
                            <tool id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.compilerDebug.1203083432" name="Arm Compiler" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.compilerDebug.1132205317">
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.INCLUDE_PATH.192241147" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.INCLUDE_PATH" valueType="includePath">
                                    <listOptionValue value="${COM_TI_MSPM0_SDK_INCLUDE_PATH}"/>
                                    <listOptionValue value="${SYSCONFIG_TOOL_INCLUDE_PATH}"/>
                                    <listOptionValue value="${PROJECT_ROOT}"/>
                                    <listOptionValue value="${PROJECT_ROOT}/DMP/Inc"/>
                                    <listOptionValue value="${PROJECT_ROOT}/${ConfigName}"/>
                                    <listOptionValue value="${COM_TI_MSPM0_SDK_INSTALL_DIR}/source/third_party/CMSIS/Core/Include"/>
                                    <listOptionValue value="${COM_TI_MSPM0_SDK_INSTALL_DIR}/source"/>
                                    <listOptionValue value="${PROJECT_ROOT}/BSP/Inc"/>
                                    <listOptionValue value="${PROJECT_ROOT}/APP/Inc"/>
                                    <listOptionValue value="${PROJECT_ROOT}/DMP/Inc"/>
                                </option>
                            </tool>
                            <tool id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.linkerDebug.292834295" name="Arm Linker" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.linkerDebug.1336043258"/>
                            <tool id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.hex.1171270611" name="Arm Hex Utility" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.hex.1929203450"/>
                            <tool id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.objcopy.2070229981" name="Arm Objcopy Utility" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.objcopy.1335563000"/>
                            <tool id="com.ti.ccstudio.buildDefinitions.sysConfig.1704059478" name="SysConfig" superClass="com.ti.ccstudio.buildDefinitions.sysConfig.1093365584"/>
                        </toolChain>
                    </folderInfo>
                    <folderInfo id="com.ti.ccstudio.buildDefinitions.TMS470.Debug.**********.BSP" name="BSP" resourcePath="BSP">
                        <toolChain id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.DebugToolchain.1774852319" name="TI Build Tools" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.DebugToolchain" unusedChildren="">
                            <option id="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS.587670663.727063543" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS.587670663"/>
                            <option id="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION.465217782.1798211682" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION.465217782"/>
                            <targetPlatform id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.targetPlatformDebug" name="Platform" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.targetPlatformDebug"/>
                            <tool id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.compilerDebug.812827449" name="Arm Compiler" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.compilerDebug.1132205317">
                                <option id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.INCLUDE_PATH.609057118" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.compilerID.INCLUDE_PATH" valueType="includePath">
                                    <listOptionValue value="${COM_TI_MSPM0_SDK_INCLUDE_PATH}"/>
                                    <listOptionValue value="${SYSCONFIG_TOOL_INCLUDE_PATH}"/>
                                    <listOptionValue value="${PROJECT_ROOT}/BSP/Inc"/>
                                    <listOptionValue value="${PROJECT_ROOT}"/>
                                    <listOptionValue value="${PROJECT_ROOT}/DMP"/>
                                    <listOptionValue value="${PROJECT_ROOT}/App/Inc"/>
                                    <listOptionValue value="${PROJECT_ROOT}/${ConfigName}"/>
                                    <listOptionValue value="${COM_TI_MSPM0_SDK_INSTALL_DIR}/source/third_party/CMSIS/Core/Include"/>
                                    <listOptionValue value="${COM_TI_MSPM0_SDK_INSTALL_DIR}/source"/>
                                </option>
                            </tool>
                            <tool id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.linkerDebug.255295407" name="Arm Linker" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.exe.linkerDebug.1336043258"/>
                            <tool id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.hex.1555554217" name="Arm Hex Utility" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.hex.1929203450"/>
                            <tool id="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.objcopy.1977633668" name="Arm Objcopy Utility" superClass="com.ti.ccstudio.buildDefinitions.TMS470_TICLANG_4.0.objcopy.1335563000"/>
                            <tool id="com.ti.ccstudio.buildDefinitions.sysConfig.310513331" name="SysConfig" superClass="com.ti.ccstudio.buildDefinitions.sysConfig.1093365584"/>
                        </toolChain>
                    </folderInfo>
                </configuration>
            </storageModule>
            <storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
        </cconfiguration>
    </storageModule>
    <storageModule moduleId="cdtBuildSystem" version="4.0.0">
        <project id="empty_LP_MSPM0G3507_nortos_ticlang.com.ti.ccstudio.buildDefinitions.TMS470.ProjectType.679861107" name="TMS470" projectType="com.ti.ccstudio.buildDefinitions.TMS470.ProjectType"/>
    </storageModule>
</cproject>
