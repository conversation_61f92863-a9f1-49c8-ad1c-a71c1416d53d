/**
 * @file NonBlockDelay.h
 * @brief 非阻塞延时管理器头文件
 * @details 提供基于系统时钟的非阻塞延时机制，替代阻塞式Delay()函数
 *          解决系统卡死和任务调度被阻塞的问题
 * @version 1.0
 * @date 2025-08-01
 * <AUTHOR> Stability Refactor Team
 */

#ifndef __NonBlockDelay_h
#define __NonBlockDelay_h

#include "SysConfig.h"

/**
 * @brief 非阻塞延时结构体
 * @details 用于管理单个延时操作的状态和时间信息
 */
typedef struct
{
    uint32_t start_time;  /**< 延时开始时间戳（毫秒） */
    uint32_t delay_time;  /**< 延时持续时间（毫秒） */
    bool active;          /**< 延时是否激活状态 */
} NonBlockDelay_t;

/* ========================= 函数声明 ========================= */

/**
 * @brief 检查非阻塞延时是否完成
 * @param delay 指向延时结构体的指针
 * @retval true 延时已完成或未激活
 * @retval false 延时仍在进行中
 * @note 此函数应在主循环或任务中周期性调用
 */
bool NonBlockDelay_Check(NonBlockDelay_t *delay);

/**
 * @brief 启动非阻塞延时
 * @param delay 指向延时结构体的指针
 * @param ms 延时时间（毫秒）
 * @note 启动延时后，需要周期性调用NonBlockDelay_Check检查完成状态
 */
void NonBlockDelay_Start(NonBlockDelay_t *delay, uint32_t ms);

/**
 * @brief 停止非阻塞延时
 * @param delay 指向延时结构体的指针
 * @note 立即停止延时，将active标志设为false
 */
void NonBlockDelay_Stop(NonBlockDelay_t *delay);

/**
 * @brief 重置非阻塞延时
 * @param delay 指向延时结构体的指针
 * @note 重置延时状态，但保持延时时间不变
 */
void NonBlockDelay_Reset(NonBlockDelay_t *delay);

/**
 * @brief 获取延时剩余时间
 * @param delay 指向延时结构体的指针
 * @retval 剩余延时时间（毫秒），如果延时未激活或已完成则返回0
 */
uint32_t NonBlockDelay_GetRemaining(NonBlockDelay_t *delay);

/**
 * @brief 检查延时是否激活
 * @param delay 指向延时结构体的指针
 * @retval true 延时正在进行中
 * @retval false 延时未激活或已完成
 */
bool NonBlockDelay_IsActive(NonBlockDelay_t *delay);

#endif /* __NonBlockDelay_h */
