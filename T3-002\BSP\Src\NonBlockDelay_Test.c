/**
 * @file NonBlockDelay_Test.c
 * @brief 非阻塞延时管理器测试示例
 * @details 演示如何使用非阻塞延时管理器替代阻塞式延时
 * @version 1.0
 * @date 2025-08-01
 * <AUTHOR> Stability Refactor Team
 */

#include "NonBlockDelay.h"

// 测试用的延时实例
static NonBlockDelay_t test_delay1;
static NonBlockDelay_t test_delay2;

/**
 * @brief 非阻塞延时使用示例1：简单延时
 * @details 演示如何在任务中使用非阻塞延时
 */
void NonBlockDelay_Example1(void)
{
    static bool delay_started = false;
    
    if (!delay_started)
    {
        // 启动1000ms延时
        NonBlockDelay_Start(&test_delay1, 1000);
        delay_started = true;
    }
    
    // 检查延时是否完成
    if (NonBlockDelay_Check(&test_delay1))
    {
        // 延时完成，执行相应操作
        // 这里可以放置延时完成后要执行的代码
        delay_started = false;  // 重置标志，准备下次延时
    }
    
    // 在延时期间，系统可以继续执行其他任务
    // 不会被阻塞
}

/**
 * @brief 非阻塞延时使用示例2：状态机延时
 * @details 演示如何在状态机中使用非阻塞延时
 */
void NonBlockDelay_Example2(void)
{
    typedef enum {
        STATE_INIT,
        STATE_DELAY1,
        STATE_PROCESS,
        STATE_DELAY2,
        STATE_COMPLETE
    } TestState_t;
    
    static TestState_t state = STATE_INIT;
    
    switch (state)
    {
        case STATE_INIT:
            // 初始化状态，启动第一个延时
            NonBlockDelay_Start(&test_delay1, 500);
            state = STATE_DELAY1;
            break;
            
        case STATE_DELAY1:
            // 等待第一个延时完成
            if (NonBlockDelay_Check(&test_delay1))
            {
                // 延时完成，进入处理状态
                state = STATE_PROCESS;
            }
            break;
            
        case STATE_PROCESS:
            // 处理状态，执行一些操作后启动第二个延时
            NonBlockDelay_Start(&test_delay2, 300);
            state = STATE_DELAY2;
            break;
            
        case STATE_DELAY2:
            // 等待第二个延时完成
            if (NonBlockDelay_Check(&test_delay2))
            {
                // 延时完成，进入完成状态
                state = STATE_COMPLETE;
            }
            break;
            
        case STATE_COMPLETE:
            // 完成状态，重新开始
            state = STATE_INIT;
            break;
            
        default:
            state = STATE_INIT;
            break;
    }
}

/**
 * @brief 非阻塞延时使用示例3：替代原有的阻塞延时
 * @details 演示如何将原有的阻塞延时代码改为非阻塞实现
 */
void NonBlockDelay_Example3_Original(void)
{
    // 原有的阻塞式代码（会导致系统卡死）
    /*
    // 执行操作1
    some_operation1();
    
    // 阻塞延时100ms
    Delay(100);
    
    // 执行操作2
    some_operation2();
    
    // 阻塞延时200ms
    Delay(200);
    
    // 执行操作3
    some_operation3();
    */
}

void NonBlockDelay_Example3_Improved(void)
{
    // 改进后的非阻塞式代码
    typedef enum {
        STEP_INIT,
        STEP_OP1_DELAY,
        STEP_OP2,
        STEP_OP2_DELAY,
        STEP_OP3,
        STEP_COMPLETE
    } ProcessStep_t;
    
    static ProcessStep_t step = STEP_INIT;
    static NonBlockDelay_t process_delay;
    
    switch (step)
    {
        case STEP_INIT:
            // 执行操作1
            // some_operation1();
            
            // 启动100ms非阻塞延时
            NonBlockDelay_Start(&process_delay, 100);
            step = STEP_OP1_DELAY;
            break;
            
        case STEP_OP1_DELAY:
            // 等待延时完成
            if (NonBlockDelay_Check(&process_delay))
            {
                step = STEP_OP2;
            }
            break;
            
        case STEP_OP2:
            // 执行操作2
            // some_operation2();
            
            // 启动200ms非阻塞延时
            NonBlockDelay_Start(&process_delay, 200);
            step = STEP_OP2_DELAY;
            break;
            
        case STEP_OP2_DELAY:
            // 等待延时完成
            if (NonBlockDelay_Check(&process_delay))
            {
                step = STEP_OP3;
            }
            break;
            
        case STEP_OP3:
            // 执行操作3
            // some_operation3();
            step = STEP_COMPLETE;
            break;
            
        case STEP_COMPLETE:
            // 处理完成，可以重新开始或退出
            step = STEP_INIT;
            break;
            
        default:
            step = STEP_INIT;
            break;
    }
}

/**
 * @brief 非阻塞延时管理器功能测试
 * @details 测试所有非阻塞延时管理器的功能
 */
void NonBlockDelay_FunctionTest(void)
{
    NonBlockDelay_t test_delay;
    
    // 测试1：基本延时功能
    NonBlockDelay_Start(&test_delay, 1000);
    
    // 测试2：检查延时状态
    bool is_active = NonBlockDelay_IsActive(&test_delay);  // 应该返回true
    
    // 测试3：获取剩余时间
    uint32_t remaining = NonBlockDelay_GetRemaining(&test_delay);
    
    // 测试4：停止延时
    NonBlockDelay_Stop(&test_delay);
    is_active = NonBlockDelay_IsActive(&test_delay);  // 应该返回false
    
    // 测试5：重置延时
    NonBlockDelay_Start(&test_delay, 500);
    NonBlockDelay_Reset(&test_delay);  // 重新开始500ms延时
}
