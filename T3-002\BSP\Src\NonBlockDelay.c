/**
 * @file NonBlockDelay.c
 * @brief 非阻塞延时管理器实现
 * @details 基于系统时钟Sys_GetTick()实现非阻塞延时机制
 *          替代阻塞式Delay()函数，解决系统卡死问题
 * @version 1.0
 * @date 2025-08-01
 * <AUTHOR> Stability Refactor Team
 */

#include "NonBlockDelay.h"

/**
 * @brief 检查非阻塞延时是否完成
 * @param delay 指向延时结构体的指针
 * @retval true 延时已完成或未激活
 * @retval false 延时仍在进行中
 * @note 此函数应在主循环或任务中周期性调用
 */
bool NonBlockDelay_Check(NonBlockDelay_t *delay)
{
    // 参数检查
    if (delay == NULL)
    {
        return true;
    }
    
    // 如果延时未激活，直接返回完成
    if (!delay->active)
    {
        return true;
    }
    
    // 获取当前时间
    uint32_t current_time = Sys_GetTick();
    
    // 处理时钟溢出情况
    uint32_t elapsed_time;
    if (current_time >= delay->start_time)
    {
        // 正常情况：当前时间大于等于开始时间
        elapsed_time = current_time - delay->start_time;
    }
    else
    {
        // 时钟溢出情况：当前时间小于开始时间
        elapsed_time = (UINT32_MAX - delay->start_time) + current_time + 1;
    }
    
    // 检查是否达到延时时间
    if (elapsed_time >= delay->delay_time)
    {
        delay->active = false;  // 延时完成，设置为非激活状态
        return true;
    }
    
    return false;  // 延时仍在进行中
}

/**
 * @brief 启动非阻塞延时
 * @param delay 指向延时结构体的指针
 * @param ms 延时时间（毫秒）
 * @note 启动延时后，需要周期性调用NonBlockDelay_Check检查完成状态
 */
void NonBlockDelay_Start(NonBlockDelay_t *delay, uint32_t ms)
{
    // 参数检查
    if (delay == NULL)
    {
        return;
    }
    
    delay->start_time = Sys_GetTick();  // 记录开始时间
    delay->delay_time = ms;             // 设置延时时间
    delay->active = true;               // 激活延时
}

/**
 * @brief 停止非阻塞延时
 * @param delay 指向延时结构体的指针
 * @note 立即停止延时，将active标志设为false
 */
void NonBlockDelay_Stop(NonBlockDelay_t *delay)
{
    // 参数检查
    if (delay == NULL)
    {
        return;
    }
    
    delay->active = false;  // 停止延时
}

/**
 * @brief 重置非阻塞延时
 * @param delay 指向延时结构体的指针
 * @note 重置延时状态，但保持延时时间不变
 */
void NonBlockDelay_Reset(NonBlockDelay_t *delay)
{
    // 参数检查
    if (delay == NULL)
    {
        return;
    }
    
    delay->start_time = Sys_GetTick();  // 重新记录开始时间
    delay->active = true;               // 重新激活延时
    // delay_time 保持不变
}

/**
 * @brief 获取延时剩余时间
 * @param delay 指向延时结构体的指针
 * @retval 剩余延时时间（毫秒），如果延时未激活或已完成则返回0
 */
uint32_t NonBlockDelay_GetRemaining(NonBlockDelay_t *delay)
{
    // 参数检查
    if (delay == NULL || !delay->active)
    {
        return 0;
    }
    
    // 获取当前时间
    uint32_t current_time = Sys_GetTick();
    
    // 处理时钟溢出情况
    uint32_t elapsed_time;
    if (current_time >= delay->start_time)
    {
        // 正常情况：当前时间大于等于开始时间
        elapsed_time = current_time - delay->start_time;
    }
    else
    {
        // 时钟溢出情况：当前时间小于开始时间
        elapsed_time = (UINT32_MAX - delay->start_time) + current_time + 1;
    }
    
    // 计算剩余时间
    if (elapsed_time >= delay->delay_time)
    {
        return 0;  // 延时已完成
    }
    else
    {
        return delay->delay_time - elapsed_time;  // 返回剩余时间
    }
}

/**
 * @brief 检查延时是否激活
 * @param delay 指向延时结构体的指针
 * @retval true 延时正在进行中
 * @retval false 延时未激活或已完成
 */
bool NonBlockDelay_IsActive(NonBlockDelay_t *delay)
{
    // 参数检查
    if (delay == NULL)
    {
        return false;
    }
    
    return delay->active;
}
