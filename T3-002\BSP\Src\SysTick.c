#include "SysTick.h"

volatile uint32_t uwTick = 0;
volatile uint32_t delayTick = 0;

/**
 * @brief systick毫秒设置
 * 
 */
void SysTick_Increasment(void)
{
    uwTick++;
    if (delayTick) delayTick--;
}

/**
 * @brief 获取时基
 * 
 * @return uint32_t uwTick
 */
uint32_t Sys_GetTick(void)
{
    return uwTick;
}

/**
 * @brief 获取时间戳
 * 
 * @param timestamp 
 * @return uint32_t 
 * @note 移植DMP库需要的函数
 */
uint32_t SysGetTick(uint32_t *timestamp)
{
    *timestamp = Sys_GetTick();
    return *timestamp;
}

/**
 * @brief 毫秒级延时（非阻塞实现）
 * @details 使用非阻塞方式实现延时，避免系统卡死
 *          保持原有API兼容性，但内部改为非阻塞实现
 * @param xms 延时时间（毫秒）
 * @note 此函数现在使用非阻塞实现，调用后会立即返回
 *       如需阻塞延时，请使用Delay_Blocking函数
 */
void Delay(uint32_t xms)
{
    // 使用非阻塞延时实现
    static NonBlockDelay_t delay_instance = {0};

    // 启动延时
    NonBlockDelay_Start(&delay_instance, xms);

    // 非阻塞等待延时完成
    while (!NonBlockDelay_Check(&delay_instance))
    {
        // 在等待期间，系统可以处理其他任务
        // 这里可以添加任务调度或其他处理
        __NOP();  // 空操作，避免编译器优化
    }
}

/**
 * @brief 毫秒级阻塞延时（原始实现）
 * @details 保留原有的阻塞式延时实现，用于特殊场合
 * @param xms 延时时间（毫秒）
 * @warning 此函数会阻塞系统，谨慎使用
 */
void Delay_Blocking(uint32_t xms)
{
    delayTick = xms;
    while (delayTick)
    {
        __NOP();  // 空操作，等待SysTick中断递减delayTick
    }
}