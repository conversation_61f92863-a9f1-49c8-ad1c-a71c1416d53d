#include "Motor.h"

// TB6612 方向控制宏定义
#define AIN1_SET()   DL_GPIO_setPins(DIRC_CTRL_PORT, DIRC_CTRL_AIN1_PIN)
#define AIN1_CLR()   DL_GPIO_clearPins(DIRC_CTRL_PORT, DIRC_CTRL_AIN1_PIN)
#define AIN2_SET()   DL_GPIO_setPins(DIRC_CTRL_PORT, DIRC_CTRL_AIN2_PIN)
#define AIN2_CLR()   DL_GPIO_clearPins(DIRC_CTRL_PORT, DIRC_CTRL_AIN2_PIN)

#define BIN1_SET()   DL_GPIO_setPins(DIRC_CTRL_PORT, DIRC_CTRL_BIN1_PIN)
#define BIN1_CLR()   DL_GPIO_clearPins(DIRC_CTRL_PORT, DIRC_CTRL_BIN1_PIN)
#define BIN2_SET()   DL_GPIO_setPins(DIRC_CTRL_PORT, DIRC_CTRL_BIN2_PIN)
#define BIN2_CLR()   DL_GPIO_clearPins(DIRC_CTRL_PORT, DIRC_CTRL_BIN2_PIN)

// 左电机
MOTOR_Def_t Motor_Left = {
    .Motor_Dirc = DIRC_FOWARD,
    .Motor_Encoder_Addr = &Data_MotorEncoder[0],
    .Motor_PWM_TIMX = Motor_PWM_INST,  // 左电机PWM定时器
    .Motor_PWM_CH = DL_TIMER_CC_0_INDEX,
    .Motor_IN1_PIN = DIRC_CTRL_AIN1_PIN,        // TB6612 AIN1引脚
    .Motor_IN2_PIN = DIRC_CTRL_AIN2_PIN         // TB6612 AIN2引脚
};

// 右电机
MOTOR_Def_t Motor_Right = {
    .Motor_Dirc = DIRC_FOWARD,
    .Motor_Encoder_Addr = &Data_MotorEncoder[1],
    .Motor_PWM_TIMX = Motor_PWM_INST,  // 右电机PWM定时器
    .Motor_PWM_CH = DL_TIMER_CC_1_INDEX,
    .Motor_IN1_PIN = DIRC_CTRL_BIN1_PIN,         // TB6612 BIN1引脚
    .Motor_IN2_PIN = DIRC_CTRL_BIN2_PIN          // TB6612 BIN2引脚
};

/**
 * @brief 开启电机
 */
void Motor_Start(void)
{
    // 开启左右电机PWM定时器
    DL_TimerG_startCounter(Motor_PWM_INST);

    // 初始占空比设为0
    Motor_SetDuty(&Motor_Left, 0.0f);
    Motor_SetDuty(&Motor_Right, 0.0f);

    // 初始化PID
    PID_IQ_Init(&Motor_Left.Motor_PID_Instance);
    PID_IQ_Init(&Motor_Right.Motor_PID_Instance);

    // 设置PID参数
    PID_IQ_SetParams(&Motor_Left.Motor_PID_Instance, 5.0f, 0.5f, 0.1f);
    PID_IQ_SetParams(&Motor_Right.Motor_PID_Instance, 5.0f, 0.5f, 0.1f);
}

/**
 * @brief 设置电机正反转（适配TB6612）
 */
static bool Motor_SetDirc(MOTOR_Def_t *Motor, Motor_DIRC_Def_t Dirc)
{
    if (Motor == NULL) return false;

    if (Dirc == DIRC_FOWARD)
    {
        // 左电机正转：AIN1=H, AIN2=L
        if (Motor == &Motor_Left)
        {
            AIN1_SET();
            AIN2_CLR();
        }
        // 右电机正转：BIN1=H, BIN2=L
        else if (Motor == &Motor_Right)
        {
            BIN1_SET();
            BIN2_CLR();
        }
        Motor->Motor_Dirc = DIRC_FOWARD;
        return true;
    }
    else if (Dirc == DIRC_BACKWARD)
    {
        // 左电机反转：AIN1=L, AIN2=H
        if (Motor == &Motor_Left)
        {
            AIN1_CLR();
            AIN2_SET();
        }
        // 右电机反转：BIN1=L, BIN2=H
        else if (Motor == &Motor_Right)
        {
            BIN1_CLR();
            BIN2_SET();
        }
        Motor->Motor_Dirc = DIRC_BACKWARD;
        return true;
    }
    return false;
}

/**
 * @brief 设置电机占空比（-100~100）
 */
bool Motor_SetDuty(MOTOR_Def_t *Motor, float value)
{
    if (Motor == NULL) return false;

    // 限制占空比范围
    if (value > 100.0f) value = 100.0f;
    if (value < -100.0f) value = -100.0f;

    // 设置方向
    Motor_SetDirc(Motor, value >= 0 ? DIRC_FOWARD : DIRC_BACKWARD);

    // 计算PWM值（假设定时器周期对应100%占空比）
    uint32_t duty = (uint32_t)fabs(value);
    DL_TimerG_setCaptureCompareValue(Motor->Motor_PWM_TIMX, duty, Motor->Motor_PWM_CH);

    return true;
}

/**
 * @brief 获取电机速度并更新到PID
 */
bool Motor_GetSpeed(MOTOR_Def_t *Motor, uint16_t time)
{
    if (Motor == NULL) return false;

    _iq Interval_Time = _IQ(time / 1000.0f);  // 转换为秒
    _iq Encoder_Value = _IQ(*Motor->Motor_Encoder_Addr);
    *Motor->Motor_Encoder_Addr = 0;  // 清空编码器值

    // 计算速度（脉冲/秒）
    _iq Speed = _IQdiv(Encoder_Value, Interval_Time);

    // 根据方向调整速度符号
    if (Motor->Motor_Dirc == DIRC_BACKWARD)
    {
        Speed = -Speed;
    }

    Motor->Motor_PID_Instance.Acutal_Now = Speed;
    return true;
}